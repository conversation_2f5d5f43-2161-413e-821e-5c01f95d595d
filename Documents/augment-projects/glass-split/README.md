# Glass Cutting Optimization using Quantum Annealing

This project implements a quantum annealing algorithm for solving the glass cutting optimization problem with guillotine cut constraints, based on the research paper:

**"Application of Ising Machine to optimize glass-plate cutting patterns with Guillotine-Cut Constraint"**  
by <PERSON><PERSON> et al. (AGC Research Report 70, 2020)

## Overview

The glass cutting problem is a combinatorial optimization challenge where the goal is to cut glass items from a larger base material while minimizing waste. This implementation uses quantum annealing through PyQUBO to solve the problem efficiently.

### Key Features

- **Quantum Annealing**: Uses PyQUBO to formulate the problem as a QUBO (Quadratic Unconstrained Binary Optimization)
- **NFDH Method**: Based on Next-Fit Decreasing Height algorithm
- **Guillotine Constraints**: Supports straight-line cuts from edge to edge
- **Multiple Solvers**: Supports both simulated annealing and exact solvers
- **Visualization**: Provides graphical representation of cutting solutions
- **Comprehensive Testing**: Includes unit tests and research paper examples

## Problem Formulation

The optimization problem is formulated as:

### Objective Function
```
minimize: Σ_i Σ_j Σ_i' x_ij x_i'j max(0, h_i' - h_i)w_i + λ_1(Σ_i Σ_j (W - Σ_i' x_i'j w_i'))^2
```

### Constraints
1. **Each item used exactly once**: `Σ_j x_ij = 1, ∀i ∈ I`
2. **Width constraint per row**: `Σ_i x_ij w_i ≤ W, ∀j ∈ J`

Where:
- `x_ij ∈ {0,1}`: Binary variable (1 if item i is placed in row j)
- `w_i, h_i`: Width and height of item i
- `W`: Material width
- `λ_1`: Penalty parameter for width constraint

## Installation

1. **Install dependencies**:
```bash
pip install -r requirements.txt
```

2. **Required packages**:
- `pyqubo>=1.4.0`: Quantum annealing formulation
- `numpy>=1.21.0`: Numerical computations
- `matplotlib>=3.5.0`: Visualization
- `neal>=0.6.0`: Simulated annealing solver
- `dimod>=0.12.0`: Exact solver (optional)

## Usage

### Basic Example

```python
from glass_cutting_optimizer import GlassCuttingOptimizer, Item

# Create optimizer
optimizer = GlassCuttingOptimizer(material_width=100.0, lambda1=1.5)

# Define items to cut
items = [
    Item(0, 40, 30),  # Item 0: width=40, height=30
    Item(1, 35, 25),  # Item 1: width=35, height=25
    Item(2, 30, 20),  # Item 2: width=30, height=20
]

# Add items and solve
optimizer.add_items(items)
solution = optimizer.solve(num_reads=1000)

# Check results
print(f"Solution: {solution}")
if solution.is_feasible:
    print(f"Total height: {solution.total_height}")
    print(f"Waste area: {solution.waste_area}")
    
    # Visualize solution
    optimizer.visualize_solution(solution)
```

### Multiple Runs (Research Paper Approach)

```python
# Solve multiple times for better statistics
solutions = optimizer.solve_multiple_runs(num_runs=10, num_reads=1000)
stats = optimizer.get_statistics(solutions)

print(f"Feasible rate: {stats['feasible_rate']:.2%}")
print(f"Best solution: {stats['best_solution']}")
```

## File Structure

```
glass-split/
├── algorithm.md                    # Research paper (Japanese/English)
├── glass_cutting_optimizer.py      # Main implementation
├── test_glass_cutting.py          # Comprehensive test suite
├── demo.py                        # Demonstration script
├── requirements.txt               # Python dependencies
├── README.md                      # This file
└── fig*.jpg/png                   # Research paper figures
```

## Running Tests

### Unit Tests
```bash
python test_glass_cutting.py
```

### Demo Script
```bash
python demo.py
```

The demo script includes:
1. **Basic Usage**: Simple example with visualization
2. **Research Paper Example**: Larger problem based on paper patterns
3. **Parameter Study**: Effect of different λ values
4. **Scaling Analysis**: Performance vs problem size

## Algorithm Details

### NFDH Method
The Next-Fit Decreasing Height method places items in rows:
1. Items are assigned to rows based on height compatibility
2. Width constraints are enforced per row
3. Waste is calculated from unused space and height differences

### Quantum Annealing Formulation
The problem is converted to QUBO format:
1. **Decision variables**: Binary variables `x_ij` for item-row assignments
2. **Objective terms**: Waste minimization and constraint penalties
3. **Solver**: Uses simulated annealing or exact solvers via PyQUBO

### Key Classes

- **`Item`**: Represents a glass item with width and height
- **`Solution`**: Contains assignment, metrics, and feasibility status
- **`GlassCuttingOptimizer`**: Main optimizer class with solving and visualization

## Research Paper Implementation

This implementation closely follows the research paper methodology:

### Experimental Setup
- Multiple problem sizes (2-4 material sheets)
- 1000 runs per problem as in the paper
- Comparison of feasible vs optimal solutions
- Statistical analysis of results

### Key Findings Reproduced
- Quantum annealing effectiveness for small-medium problems
- Trade-off between problem size and solution quality
- Importance of parameter tuning (λ values)

## Performance Characteristics

### Strengths
- Effective for small to medium-sized problems (≤10 items)
- Finds good solutions quickly with quantum annealing
- Handles guillotine cut constraints naturally
- Provides statistical analysis across multiple runs

### Limitations
- Scalability limited by QUBO size
- May require parameter tuning for different problem types
- Quantum advantage depends on problem structure

## Future Extensions

Based on the research paper suggestions:
1. **Item Rotation**: Support for 90-degree rotations
2. **Multi-stage Cutting**: Extension to n-stage algorithms
3. **Real Quantum Hardware**: Integration with D-Wave systems
4. **Hybrid Algorithms**: CPU-quantum hybrid approaches

## References

1. Kida, T., et al. "Application of Ising Machine to optimize glass-plate cutting patterns with Guillotine-Cut Constraint." AGC Research Report 70 (2020)
2. PyQUBO Documentation: https://github.com/recruit-communications/pyqubo
3. D-Wave Ocean SDK: https://docs.ocean.dwavesys.com/

## License

This implementation is for research and educational purposes, based on the published research paper by AGC Corporation and NTT Data.
