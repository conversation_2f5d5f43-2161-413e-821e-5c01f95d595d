"""
Glass Cutting Optimization using Quantum Annealing with PyQUBO

This module implements the quantum annealing algorithm for the glass cutting problem
with guillotine cut constraints based on the NFDH (Next-Fit Decreasing Height) method.

The implementation follows the research paper:
"Application of Ising Machine to optimize glass-plate cutting patterns with Guillotine-Cut Constraint"
by <PERSON><PERSON> et al. (AGC Research Report 70, 2020)
"""

import numpy as np
import pyqubo
from typing import List, Tuple, Dict, Optional
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from dataclasses import dataclass
import time


@dataclass
class Item:
    """Represents a glass item to be cut"""
    id: int
    width: float
    height: float
    
    def __str__(self):
        return f"Item({self.id}: {self.width}x{self.height})"


@dataclass
class Solution:
    """Represents a solution to the glass cutting problem"""
    assignment: Dict[int, int]  # item_id -> row_id mapping
    total_height: float
    waste_area: float
    is_feasible: bool
    energy: float
    
    def __str__(self):
        return f"Solution(height={self.total_height:.2f}, waste={self.waste_area:.2f}, feasible={self.is_feasible})"


class GlassCuttingOptimizer:
    """
    Quantum annealing optimizer for glass cutting problem using PyQUBO
    
    Based on the NFDH method with Ising machine formulation:
    - Objective: Minimize waste area (loss)
    - Constraints: Each item used exactly once, width constraints per row
    """
    
    def __init__(self, material_width: float, lambda1: float = 1.0):
        """
        Initialize the optimizer
        
        Args:
            material_width: Width of the base material
            lambda1: Weight parameter for width constraint penalty
        """
        self.material_width = material_width
        self.lambda1 = lambda1
        self.items: List[Item] = []
        self.num_rows: int = 0
        
    def add_items(self, items: List[Item]):
        """Add items to be optimized"""
        self.items = items
        # Estimate number of rows needed (conservative upper bound)
        total_area = sum(item.width * item.height for item in items)
        avg_height = sum(item.height for item in items) / len(items)
        self.num_rows = min(len(items), max(3, int(total_area / (self.material_width * avg_height)) + 2))
        
    def _create_qubo_model(self) -> pyqubo.Model:
        """
        Create QUBO model based on the research paper formulation
        
        Objective function (equation 2):
        argmin_x (Σ_i Σ_j Σ_i' x_ij x_i'j max(0, h_i' - h_i)w_i + λ_1(Σ_i Σ_j (W - Σ_i' x_i'j w_i'))^2)
        
        Constraints:
        - Each item used exactly once (equation 5): Σ_j x_ij = 1, ∀i
        - Width constraint per row (equation 6): Σ_i x_ij w_i ≤ W, ∀j
        """
        # Decision variables: x[i][j] = 1 if item i is placed in row j
        x = {}
        for i in range(len(self.items)):
            x[i] = {}
            for j in range(self.num_rows):
                x[i][j] = pyqubo.Binary(f'x_{i}_{j}')
        
        # Objective function - minimize waste (loss)
        objective = 0
        
        # First term: waste calculation based on height differences
        for j in range(self.num_rows):
            for i in range(len(self.items)):
                for i_prime in range(len(self.items)):
                    if i != i_prime:
                        height_diff = max(0, self.items[i_prime].height - self.items[i].height)
                        objective += x[i][j] * x[i_prime][j] * height_diff * self.items[i].width
        
        # Second term: width constraint penalty
        for j in range(self.num_rows):
            width_usage = sum(x[i][j] * self.items[i].width for i in range(len(self.items)))
            width_violation = (self.material_width - width_usage) ** 2
            objective += self.lambda1 * width_violation
        
        # Constraint 1: Each item used exactly once
        constraint1 = 0
        for i in range(len(self.items)):
            constraint1 += (sum(x[i][j] for j in range(self.num_rows)) - 1) ** 2
        
        # Constraint 2: Width limit per row (soft constraint, already in objective)
        # This is handled by the penalty term in the objective
        
        # Total Hamiltonian
        H = objective + 10.0 * constraint1  # High penalty for constraint violation
        
        # Compile model
        model = H.compile()
        return model
        
    def solve(self, num_reads: int = 1000, sampler_type: str = 'simulated_annealing') -> Solution:
        """
        Solve the glass cutting optimization problem
        
        Args:
            num_reads: Number of samples to generate
            sampler_type: Type of sampler ('simulated_annealing' or 'exact')
            
        Returns:
            Best solution found
        """
        if not self.items:
            raise ValueError("No items added to optimize")
            
        # Create QUBO model
        model = self._create_qubo_model()
        
        # Choose sampler
        if sampler_type == 'simulated_annealing':
            try:
                import neal
                sampler = neal.SimulatedAnnealingSampler()
                sampleset = sampler.sample_qubo(model.to_qubo()[0], num_reads=num_reads)
            except ImportError:
                # Fallback to built-in simulated annealing
                print("Warning: neal not available, using built-in simulated annealing")
                sampleset = self._builtin_simulated_annealing(model.to_qubo()[0], num_reads)
        elif sampler_type == 'exact':
            try:
                import dimod
                sampler = dimod.ExactSolver()
                sampleset = sampler.sample_qubo(model.to_qubo()[0])
            except ImportError:
                raise ImportError("dimod package required for exact solver")
        else:
            raise ValueError(f"Unknown sampler type: {sampler_type}")
        
        # Decode best solution
        best_sample = sampleset.first.sample
        decoded = model.decode_sample(best_sample, vartype='BINARY')
        
        return self._create_solution(decoded.sample, sampleset.first.energy)
    
    def _create_solution(self, sample: Dict, energy: float) -> Solution:
        """Create Solution object from decoded sample"""
        assignment = {}
        
        # Extract assignment from binary variables
        for var_name, value in sample.items():
            if var_name.startswith('x_') and value == 1:
                parts = var_name.split('_')
                item_id = int(parts[1])
                row_id = int(parts[2])
                assignment[item_id] = row_id
        
        # Calculate solution metrics
        is_feasible = self._check_feasibility(assignment)
        total_height, waste_area = self._calculate_metrics(assignment)
        
        return Solution(
            assignment=assignment,
            total_height=total_height,
            waste_area=waste_area,
            is_feasible=is_feasible,
            energy=energy
        )

    def _builtin_simulated_annealing(self, qubo_dict, num_reads):
        """Built-in simulated annealing implementation as fallback"""
        import random

        # Simple sampleset-like object
        class SimpleSampleSet:
            def __init__(self, samples, energies):
                self.samples = samples
                self.energies = energies
                self.first = self

            @property
            def sample(self):
                return self.samples[0]

            @property
            def energy(self):
                return self.energies[0]

        variables = list(qubo_dict.keys())
        if not variables:
            return SimpleSampleSet([{}], [0.0])

        # Handle both single variables and pairs
        single_vars = set()
        for key in variables:
            if isinstance(key, tuple):
                single_vars.update(key)
            else:
                single_vars.add(key)
        single_vars = sorted(single_vars)

        best_sample = None
        best_energy = float('inf')

        for _ in range(num_reads):
            # Random initial solution
            sample = {var: random.randint(0, 1) for var in single_vars}

            # Calculate energy
            energy = 0.0
            for key, coeff in qubo_dict.items():
                if isinstance(key, tuple):
                    var1, var2 = key
                    energy += coeff * sample[var1] * sample[var2]
                else:
                    energy += coeff * sample[key]

            # Simple hill climbing (not full SA, but works for demo)
            for _ in range(10):  # Limited local search
                # Try flipping each variable
                for var in single_vars:
                    old_val = sample[var]
                    sample[var] = 1 - old_val

                    # Calculate new energy
                    new_energy = 0.0
                    for key, coeff in qubo_dict.items():
                        if isinstance(key, tuple):
                            var1, var2 = key
                            new_energy += coeff * sample[var1] * sample[var2]
                        else:
                            new_energy += coeff * sample[key]

                    if new_energy < energy:
                        energy = new_energy
                    else:
                        sample[var] = old_val  # Revert

            if energy < best_energy:
                best_energy = energy
                best_sample = sample.copy()

        return SimpleSampleSet([best_sample], [best_energy])
    
    def _check_feasibility(self, assignment: Dict[int, int]) -> bool:
        """Check if solution satisfies all constraints"""
        # Check if all items are assigned
        if len(assignment) != len(self.items):
            return False
            
        # Check width constraints for each row
        row_widths = {}
        for item_id, row_id in assignment.items():
            if row_id not in row_widths:
                row_widths[row_id] = 0
            row_widths[row_id] += self.items[item_id].width
            
        for row_id, width in row_widths.items():
            if width > self.material_width:
                return False
                
        return True
    
    def _calculate_metrics(self, assignment: Dict[int, int]) -> Tuple[float, float]:
        """Calculate total height and waste area"""
        if not assignment:
            return 0.0, 0.0
            
        # Group items by row
        rows = {}
        for item_id, row_id in assignment.items():
            if row_id not in rows:
                rows[row_id] = []
            rows[row_id].append(self.items[item_id])
        
        total_height = 0.0
        total_waste = 0.0
        
        for row_id, row_items in rows.items():
            if not row_items:
                continue
                
            # Row height is maximum height of items in the row
            row_height = max(item.height for item in row_items)
            total_height += row_height
            
            # Row width usage
            row_width = sum(item.width for item in row_items)
            
            # Waste in this row
            unused_width = self.material_width - row_width
            row_waste = unused_width * row_height
            
            # Add waste from height differences (NFDH method)
            for item in row_items:
                height_waste = (row_height - item.height) * item.width
                row_waste += height_waste
                
            total_waste += row_waste
            
        return total_height, total_waste

    def visualize_solution(self, solution: Solution, save_path: Optional[str] = None):
        """
        Visualize the cutting solution

        Args:
            solution: Solution to visualize
            save_path: Optional path to save the figure
        """
        if not solution.assignment:
            print("No solution to visualize")
            return

        # Group items by row
        rows = {}
        for item_id, row_id in solution.assignment.items():
            if row_id not in rows:
                rows[row_id] = []
            rows[row_id].append((item_id, self.items[item_id]))

        # Create figure
        _, ax = plt.subplots(1, 1, figsize=(12, 8))

        current_y = 0
        colors = plt.cm.Set3(np.linspace(0, 1, len(self.items)))

        for row_id in sorted(rows.keys()):
            row_items = rows[row_id]
            if not row_items:
                continue

            # Calculate row height
            row_height = max(item.height for _, item in row_items)

            # Place items in row
            current_x = 0
            for item_id, item in row_items:
                # Draw item rectangle
                rect = patches.Rectangle(
                    (current_x, current_y),
                    item.width,
                    item.height,
                    linewidth=1,
                    edgecolor='black',
                    facecolor=colors[item_id],
                    alpha=0.7
                )
                ax.add_patch(rect)

                # Add item label
                ax.text(
                    current_x + item.width/2,
                    current_y + item.height/2,
                    f'{item.id}',
                    ha='center',
                    va='center',
                    fontsize=10,
                    fontweight='bold'
                )

                current_x += item.width

            # Draw row boundary
            ax.axhline(y=current_y + row_height, color='red', linestyle='--', alpha=0.5)

            current_y += row_height

        # Draw material boundary
        ax.axvline(x=self.material_width, color='red', linewidth=2, label='Material Width')

        # Set plot properties
        ax.set_xlim(0, max(self.material_width * 1.1, current_x * 1.1))
        ax.set_ylim(0, current_y * 1.1)
        ax.set_xlabel('Width')
        ax.set_ylabel('Height')
        ax.set_title(f'Glass Cutting Solution\n'
                    f'Total Height: {solution.total_height:.2f}, '
                    f'Waste Area: {solution.waste_area:.2f}, '
                    f'Feasible: {solution.is_feasible}')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def solve_multiple_runs(self, num_runs: int = 10, num_reads: int = 1000) -> List[Solution]:
        """
        Solve the problem multiple times and return all solutions

        Args:
            num_runs: Number of independent runs
            num_reads: Number of reads per run

        Returns:
            List of solutions from all runs
        """
        solutions = []

        for run in range(num_runs):
            print(f"Run {run + 1}/{num_runs}")
            solution = self.solve(num_reads=num_reads)
            solutions.append(solution)

        return solutions

    def get_statistics(self, solutions: List[Solution]) -> Dict:
        """Get statistics from multiple solutions"""
        feasible_solutions = [s for s in solutions if s.is_feasible]

        if not feasible_solutions:
            return {
                'total_runs': len(solutions),
                'feasible_count': 0,
                'feasible_rate': 0.0,
                'best_solution': None,
                'avg_height': None,
                'avg_waste': None
            }

        best_solution = min(feasible_solutions, key=lambda s: s.waste_area)

        return {
            'total_runs': len(solutions),
            'feasible_count': len(feasible_solutions),
            'feasible_rate': len(feasible_solutions) / len(solutions),
            'best_solution': best_solution,
            'avg_height': np.mean([s.total_height for s in feasible_solutions]),
            'avg_waste': np.mean([s.waste_area for s in feasible_solutions]),
            'std_height': np.std([s.total_height for s in feasible_solutions]),
            'std_waste': np.std([s.waste_area for s in feasible_solutions])
        }
