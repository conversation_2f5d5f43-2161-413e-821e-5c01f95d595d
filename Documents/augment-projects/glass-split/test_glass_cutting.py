"""
Test suite for the Glass Cutting Optimization algorithm

This module contains comprehensive tests for the quantum annealing-based
glass cutting optimizer, including unit tests and integration tests
based on the research paper examples.
"""

import unittest
import numpy as np
import matplotlib.pyplot as plt
from glass_cutting_optimizer import Glass<PERSON>uttingOptimizer, Item, Solution
import tempfile
import os


class TestGlassCuttingOptimizer(unittest.TestCase):
    """Test cases for the GlassCuttingOptimizer class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.material_width = 100.0
        self.optimizer = GlassCuttingOptimizer(material_width=self.material_width)
        
        # Test items based on research paper patterns
        self.test_items_small = [
            Item(0, 30, 20),  # Small items
            Item(1, 25, 15),
            Item(2, 35, 25),
        ]
        
        self.test_items_medium = [
            Item(0, 40, 30),
            Item(1, 35, 25),
            Item(2, 30, 20),
            Item(3, 25, 35),
            Item(4, 20, 15),
        ]
        
        # Larger test case similar to research paper
        self.test_items_large = [
            Item(0, 45, 35),  # Pattern A-like
            Item(1, 30, 25),
            Item(2, 25, 20),
            Item(3, 40, 30),  # Pattern B-like
            Item(4, 35, 28),
            Item(5, 20, 15),
            Item(6, 50, 40),  # Pattern C-like
            Item(7, 30, 25),
            Item(8, 25, 30),  # Pattern D-like
            Item(9, 35, 20),
        ]
    
    def test_optimizer_initialization(self):
        """Test optimizer initialization"""
        self.assertEqual(self.optimizer.material_width, self.material_width)
        self.assertEqual(len(self.optimizer.items), 0)
        self.assertEqual(self.optimizer.num_rows, 0)
    
    def test_add_items(self):
        """Test adding items to optimizer"""
        self.optimizer.add_items(self.test_items_small)
        self.assertEqual(len(self.optimizer.items), 3)
        self.assertGreater(self.optimizer.num_rows, 0)
        
        # Check items are stored correctly
        for i, item in enumerate(self.test_items_small):
            self.assertEqual(self.optimizer.items[i].id, item.id)
            self.assertEqual(self.optimizer.items[i].width, item.width)
            self.assertEqual(self.optimizer.items[i].height, item.height)
    
    def test_solve_small_problem(self):
        """Test solving a small problem"""
        self.optimizer.add_items(self.test_items_small)
        
        # Test with simulated annealing
        solution = self.optimizer.solve(num_reads=100, sampler_type='simulated_annealing')
        
        self.assertIsInstance(solution, Solution)
        self.assertIsInstance(solution.assignment, dict)
        self.assertIsInstance(solution.total_height, float)
        self.assertIsInstance(solution.waste_area, float)
        self.assertIsInstance(solution.is_feasible, bool)
        
        # Basic sanity checks
        self.assertGreaterEqual(solution.total_height, 0)
        self.assertGreaterEqual(solution.waste_area, 0)
    
    def test_solve_exact_small_problem(self):
        """Test solving with exact solver for small problems"""
        # Use very small problem for exact solver
        small_items = [Item(0, 30, 20), Item(1, 25, 15)]
        self.optimizer.add_items(small_items)
        
        try:
            solution = self.optimizer.solve(sampler_type='exact')
            self.assertIsInstance(solution, Solution)
        except ImportError:
            self.skipTest("dimod not available for exact solver")
    
    def test_feasibility_check(self):
        """Test feasibility checking"""
        self.optimizer.add_items(self.test_items_small)
        
        # Test feasible assignment
        feasible_assignment = {0: 0, 1: 0, 2: 1}  # Items 0,1 in row 0, item 2 in row 1
        self.assertTrue(self.optimizer._check_feasibility(feasible_assignment))
        
        # Test infeasible assignment (missing item)
        infeasible_assignment = {0: 0, 1: 0}  # Missing item 2
        self.assertFalse(self.optimizer._check_feasibility(infeasible_assignment))
        
        # Test width constraint violation
        width_violation = {0: 0, 1: 0, 2: 0}  # All items in one row (might exceed width)
        total_width = sum(item.width for item in self.test_items_small)
        if total_width > self.material_width:
            self.assertFalse(self.optimizer._check_feasibility(width_violation))
    
    def test_metrics_calculation(self):
        """Test calculation of solution metrics"""
        self.optimizer.add_items(self.test_items_small)
        
        # Test with simple assignment
        assignment = {0: 0, 1: 1, 2: 2}  # Each item in separate row
        height, waste = self.optimizer._calculate_metrics(assignment)
        
        expected_height = sum(item.height for item in self.test_items_small)
        self.assertAlmostEqual(height, expected_height, places=2)
        self.assertGreaterEqual(waste, 0)
    
    def test_multiple_runs(self):
        """Test multiple runs functionality"""
        self.optimizer.add_items(self.test_items_small)
        
        solutions = self.optimizer.solve_multiple_runs(num_runs=3, num_reads=50)
        self.assertEqual(len(solutions), 3)
        
        for solution in solutions:
            self.assertIsInstance(solution, Solution)
    
    def test_statistics(self):
        """Test statistics calculation"""
        self.optimizer.add_items(self.test_items_small)
        
        solutions = self.optimizer.solve_multiple_runs(num_runs=5, num_reads=50)
        stats = self.optimizer.get_statistics(solutions)
        
        self.assertIn('total_runs', stats)
        self.assertIn('feasible_count', stats)
        self.assertIn('feasible_rate', stats)
        self.assertIn('best_solution', stats)
        
        self.assertEqual(stats['total_runs'], 5)
        self.assertGreaterEqual(stats['feasible_count'], 0)
        self.assertLessEqual(stats['feasible_count'], 5)
    
    def test_visualization(self):
        """Test solution visualization"""
        self.optimizer.add_items(self.test_items_small)
        solution = self.optimizer.solve(num_reads=50)
        
        # Test visualization without saving
        try:
            # Redirect matplotlib to non-interactive backend for testing
            import matplotlib
            matplotlib.use('Agg')
            self.optimizer.visualize_solution(solution)
        except Exception as e:
            self.fail(f"Visualization failed: {e}")
        
        # Test visualization with saving
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            try:
                self.optimizer.visualize_solution(solution, save_path=tmp.name)
                self.assertTrue(os.path.exists(tmp.name))
            finally:
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)


class TestResearchPaperExamples(unittest.TestCase):
    """Test cases based on the research paper examples"""
    
    def setUp(self):
        """Set up test cases based on research paper"""
        self.material_width = 200.0  # Assuming larger material for paper examples
        self.optimizer = GlassCuttingOptimizer(material_width=self.material_width)
    
    def test_pattern_combination_1(self):
        """Test combination pattern 1 (B, D) from Table 1"""
        # Approximate items based on Fig. 2 patterns B and D
        pattern_b_d = [
            Item(0, 80, 60),   # Large item from pattern B
            Item(1, 40, 30),   # Medium item
            Item(2, 30, 25),   # Small item
            Item(3, 90, 70),   # Large item from pattern D
            Item(4, 35, 40),   # Medium item
            Item(5, 25, 20),   # Small item
        ]
        
        self.optimizer.add_items(pattern_b_d)
        solution = self.optimizer.solve(num_reads=500)
        
        # Basic validation
        self.assertIsInstance(solution, Solution)
        if solution.is_feasible:
            self.assertGreater(solution.total_height, 0)
            self.assertGreaterEqual(solution.waste_area, 0)
    
    def test_pattern_combination_9(self):
        """Test combination pattern 9 (A, B, C, D) from Table 1"""
        # All patterns combined - most complex case
        all_patterns = [
            # Pattern A
            Item(0, 70, 50),
            Item(1, 35, 25),
            Item(2, 30, 30),
            # Pattern B
            Item(3, 80, 60),
            Item(4, 40, 30),
            Item(5, 30, 25),
            # Pattern C
            Item(6, 75, 55),
            Item(7, 45, 35),
            Item(8, 25, 20),
            # Pattern D
            Item(9, 90, 70),
            Item(10, 35, 40),
            Item(11, 25, 20),
        ]
        
        self.optimizer.add_items(all_patterns)
        
        # Run multiple times as suggested in the paper
        solutions = self.optimizer.solve_multiple_runs(num_runs=10, num_reads=1000)
        stats = self.optimizer.get_statistics(solutions)
        
        # Validate results
        self.assertEqual(stats['total_runs'], 10)
        
        if stats['feasible_count'] > 0:
            self.assertIsNotNone(stats['best_solution'])
            self.assertGreater(stats['avg_height'], 0)
            self.assertGreaterEqual(stats['avg_waste'], 0)
            
            print(f"Pattern 9 Results:")
            print(f"  Feasible solutions: {stats['feasible_count']}/10")
            print(f"  Feasible rate: {stats['feasible_rate']:.2%}")
            print(f"  Best solution: {stats['best_solution']}")
    
    def test_performance_scaling(self):
        """Test performance with different problem sizes"""
        sizes = [3, 5, 8]  # Different numbers of items
        results = {}
        
        for size in sizes:
            # Generate items of varying sizes
            items = []
            for i in range(size):
                width = 20 + (i % 5) * 10  # Widths from 20 to 60
                height = 15 + (i % 4) * 8   # Heights from 15 to 39
                items.append(Item(i, width, height))
            
            optimizer = GlassCuttingOptimizer(material_width=150.0)
            optimizer.add_items(items)
            
            import time
            start_time = time.time()
            solution = optimizer.solve(num_reads=200)
            solve_time = time.time() - start_time
            
            results[size] = {
                'solution': solution,
                'solve_time': solve_time,
                'feasible': solution.is_feasible
            }
        
        # Validate that we can solve problems of different sizes
        for size, result in results.items():
            self.assertIsInstance(result['solution'], Solution)
            self.assertGreater(result['solve_time'], 0)
            print(f"Size {size}: {result['solve_time']:.3f}s, feasible: {result['feasible']}")


def run_comprehensive_test():
    """Run a comprehensive test of the optimizer"""
    print("Running comprehensive glass cutting optimization test...")
    
    # Create optimizer
    optimizer = GlassCuttingOptimizer(material_width=120.0, lambda1=2.0)
    
    # Create test items
    test_items = [
        Item(0, 45, 35),
        Item(1, 30, 25),
        Item(2, 25, 20),
        Item(3, 40, 30),
        Item(4, 35, 28),
        Item(5, 20, 15),
    ]
    
    optimizer.add_items(test_items)
    
    print(f"Problem setup:")
    print(f"  Material width: {optimizer.material_width}")
    print(f"  Number of items: {len(test_items)}")
    print(f"  Items: {[str(item) for item in test_items]}")
    
    # Solve multiple times
    print("\nSolving with multiple runs...")
    solutions = optimizer.solve_multiple_runs(num_runs=20, num_reads=500)
    
    # Get statistics
    stats = optimizer.get_statistics(solutions)
    
    print(f"\nResults:")
    print(f"  Total runs: {stats['total_runs']}")
    print(f"  Feasible solutions: {stats['feasible_count']}")
    print(f"  Feasible rate: {stats['feasible_rate']:.2%}")
    
    if stats['best_solution']:
        print(f"  Best solution: {stats['best_solution']}")
        print(f"  Average height: {stats['avg_height']:.2f} ± {stats['std_height']:.2f}")
        print(f"  Average waste: {stats['avg_waste']:.2f} ± {stats['std_waste']:.2f}")
        
        # Visualize best solution
        print("\nVisualizing best solution...")
        optimizer.visualize_solution(stats['best_solution'])
    else:
        print("  No feasible solutions found")


if __name__ == '__main__':
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    
    # Run comprehensive test
    run_comprehensive_test()
