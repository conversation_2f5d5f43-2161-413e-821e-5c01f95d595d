#!/usr/bin/env python3
"""
Example usage of the Glass Cutting Optimization algorithm

This script demonstrates the key features of the quantum annealing-based
glass cutting optimizer with practical examples.
"""

from glass_cutting_optimizer import GlassCuttingOptimizer, Item
import matplotlib
matplotlib.use('Agg')  # Use non-GUI backend for compatibility


def example_1_basic_usage():
    """Example 1: Basic usage with small problem"""
    print("="*60)
    print("EXAMPLE 1: Basic Usage")
    print("="*60)
    
    # Create optimizer with material width of 100 units
    optimizer = GlassCuttingOptimizer(material_width=100.0, lambda1=2.0)
    
    # Define glass items to cut (width, height)
    items = [
        Item(0, 40, 30),  # Item 0: 40x30
        Item(1, 35, 25),  # Item 1: 35x25  
        Item(2, 30, 20),  # Item 2: 30x20
        Item(3, 25, 15),  # Item 3: 25x15
    ]
    
    print(f"Material width: {optimizer.material_width}")
    print(f"Items to cut:")
    for item in items:
        print(f"  {item}")
    
    total_width = sum(item.width for item in items)
    print(f"Total width if all in one row: {total_width}")
    
    # Add items and solve
    optimizer.add_items(items)
    solution = optimizer.solve(num_reads=500)
    
    print(f"\nSolution: {solution}")
    
    if solution.is_feasible:
        print(f"\nDetailed assignment:")
        for item_id, row_id in solution.assignment.items():
            item = items[item_id]
            print(f"  Item {item_id} ({item.width}x{item.height}) -> Row {row_id}")
        
        # Calculate efficiency
        total_material_area = optimizer.material_width * solution.total_height
        total_item_area = sum(item.width * item.height for item in items)
        efficiency = (total_item_area / total_material_area) * 100
        print(f"\nEfficiency: {efficiency:.1f}% (item area / total material area)")
        
        # Save visualization
        optimizer.visualize_solution(solution, save_path="example1_solution.png")
        print(f"Visualization saved to 'example1_solution.png'")
    else:
        print("No feasible solution found!")


def example_2_multiple_runs():
    """Example 2: Multiple runs for statistical analysis"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Multiple Runs Analysis")
    print("="*60)
    
    # Create a more challenging problem
    optimizer = GlassCuttingOptimizer(material_width=120.0, lambda1=3.0)
    
    items = [
        Item(0, 50, 40),
        Item(1, 45, 35),
        Item(2, 40, 30),
        Item(3, 35, 25),
        Item(4, 30, 20),
        Item(5, 25, 15),
    ]
    
    print(f"Material width: {optimizer.material_width}")
    print(f"Number of items: {len(items)}")
    print(f"Items: {[f'{item.width}x{item.height}' for item in items]}")
    
    optimizer.add_items(items)
    
    # Run multiple times for statistics
    print(f"\nRunning 15 independent optimizations...")
    solutions = optimizer.solve_multiple_runs(num_runs=15, num_reads=300)
    
    # Analyze results
    stats = optimizer.get_statistics(solutions)
    
    print(f"\nStatistical Results:")
    print(f"  Total runs: {stats['total_runs']}")
    print(f"  Feasible solutions: {stats['feasible_count']}")
    print(f"  Success rate: {stats['feasible_rate']:.1%}")
    
    if stats['best_solution']:
        print(f"  Best solution: {stats['best_solution']}")
        print(f"  Average height: {stats['avg_height']:.2f} ± {stats['std_height']:.2f}")
        print(f"  Average waste: {stats['avg_waste']:.2f} ± {stats['std_waste']:.2f}")
        
        # Visualize best solution
        optimizer.visualize_solution(stats['best_solution'], save_path="example2_best_solution.png")
        print(f"Best solution visualization saved to 'example2_best_solution.png'")
    else:
        print("  No feasible solutions found")


def example_3_parameter_tuning():
    """Example 3: Effect of parameter tuning"""
    print("\n" + "="*60)
    print("EXAMPLE 3: Parameter Tuning")
    print("="*60)
    
    # Fixed problem setup
    material_width = 100.0
    items = [
        Item(0, 45, 35),
        Item(1, 40, 30),
        Item(2, 35, 25),
        Item(3, 30, 20),
    ]
    
    print(f"Material width: {material_width}")
    print(f"Items: {[f'{item.width}x{item.height}' for item in items]}")
    
    # Test different lambda values
    lambda_values = [0.5, 1.0, 2.0, 5.0, 10.0]
    
    print(f"\nTesting lambda values: {lambda_values}")
    print(f"{'Lambda':<8} {'Feasible':<10} {'Height':<10} {'Waste':<10}")
    print("-" * 40)
    
    best_lambda = None
    best_waste = float('inf')
    
    for lambda_val in lambda_values:
        optimizer = GlassCuttingOptimizer(material_width=material_width, lambda1=lambda_val)
        optimizer.add_items(items)
        
        # Run a few times and take best
        solutions = optimizer.solve_multiple_runs(num_runs=5, num_reads=200)
        stats = optimizer.get_statistics(solutions)
        
        if stats['best_solution'] and stats['best_solution'].is_feasible:
            feasible = "Yes"
            height = f"{stats['best_solution'].total_height:.1f}"
            waste = f"{stats['best_solution'].waste_area:.1f}"
            
            if stats['best_solution'].waste_area < best_waste:
                best_waste = stats['best_solution'].waste_area
                best_lambda = lambda_val
        else:
            feasible = "No"
            height = "N/A"
            waste = "N/A"
        
        print(f"{lambda_val:<8} {feasible:<10} {height:<10} {waste:<10}")
    
    if best_lambda is not None:
        print(f"\nBest lambda value: {best_lambda} (lowest waste: {best_waste:.1f})")
    else:
        print(f"\nNo feasible solutions found for any lambda value")


def example_4_real_world_scenario():
    """Example 4: Real-world glass cutting scenario"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Real-World Glass Cutting Scenario")
    print("="*60)
    
    # Simulate a real glass cutting order
    # Standard glass sheet: 2000mm x 3000mm (scaled down for demo)
    material_width = 200.0  # Representing 2000mm
    
    # Customer order: various window sizes (scaled down)
    items = [
        # Large windows
        Item(0, 80, 120),  # 800x1200mm
        Item(1, 80, 120),  # 800x1200mm (duplicate)
        Item(2, 100, 150), # 1000x1500mm
        
        # Medium windows  
        Item(3, 60, 90),   # 600x900mm
        Item(4, 60, 90),   # 600x900mm (duplicate)
        Item(5, 70, 100),  # 700x1000mm
        
        # Small windows
        Item(6, 40, 60),   # 400x600mm
        Item(7, 40, 60),   # 400x600mm (duplicate)
        Item(8, 50, 70),   # 500x700mm
        Item(9, 30, 50),   # 300x500mm
    ]
    
    print(f"Glass sheet width: {material_width} units (representing 2000mm)")
    print(f"Customer order ({len(items)} pieces):")
    for i, item in enumerate(items):
        print(f"  {i+1:2d}. {item.width:3.0f}x{item.height:3.0f} units")
    
    # Calculate total area
    total_item_area = sum(item.width * item.height for item in items)
    print(f"\nTotal glass area needed: {total_item_area:.0f} square units")
    
    # Optimize cutting pattern
    optimizer = GlassCuttingOptimizer(material_width=material_width, lambda1=2.0)
    optimizer.add_items(items)
    
    print(f"\nOptimizing cutting pattern...")
    solutions = optimizer.solve_multiple_runs(num_runs=10, num_reads=1000)
    stats = optimizer.get_statistics(solutions)
    
    if stats['best_solution'] and stats['best_solution'].is_feasible:
        solution = stats['best_solution']
        total_material_area = material_width * solution.total_height
        efficiency = (total_item_area / total_material_area) * 100
        waste_percentage = (solution.waste_area / total_material_area) * 100
        
        print(f"\nOptimization Results:")
        print(f"  Success rate: {stats['feasible_rate']:.1%}")
        print(f"  Sheet height needed: {solution.total_height:.1f} units")
        print(f"  Total material area: {total_material_area:.0f} square units")
        print(f"  Waste area: {solution.waste_area:.0f} square units")
        print(f"  Material efficiency: {efficiency:.1f}%")
        print(f"  Waste percentage: {waste_percentage:.1f}%")
        
        # Show cutting pattern
        print(f"\nCutting Pattern:")
        rows = {}
        for item_id, row_id in solution.assignment.items():
            if row_id not in rows:
                rows[row_id] = []
            rows[row_id].append((item_id, items[item_id]))
        
        for row_id in sorted(rows.keys()):
            row_items = rows[row_id]
            row_width = sum(item.width for _, item in row_items)
            row_height = max(item.height for _, item in row_items)
            print(f"  Row {row_id}: {len(row_items)} pieces, "
                  f"width={row_width:.0f}/{material_width:.0f}, height={row_height:.0f}")
            for item_id, item in row_items:
                print(f"    - Piece {item_id+1}: {item.width:.0f}x{item.height:.0f}")
        
        # Save visualization
        optimizer.visualize_solution(solution, save_path="example4_real_world.png")
        print(f"\nCutting pattern visualization saved to 'example4_real_world.png'")
    else:
        print(f"\nOptimization failed - no feasible solutions found")
        print(f"Consider:")
        print(f"  - Using wider glass sheets")
        print(f"  - Splitting the order into multiple sheets")
        print(f"  - Adjusting algorithm parameters")


def main():
    """Run all examples"""
    print("Glass Cutting Optimization - Quantum Annealing Examples")
    print("Based on PyQUBO and the NFDH method")
    
    try:
        example_1_basic_usage()
        example_2_multiple_runs()
        example_3_parameter_tuning()
        example_4_real_world_scenario()
        
        print("\n" + "="*60)
        print("All examples completed successfully!")
        print("\nGenerated files:")
        print("  - example1_solution.png")
        print("  - example2_best_solution.png")
        print("  - example4_real_world.png")
        print("="*60)
        
    except Exception as e:
        print(f"\nError during examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
