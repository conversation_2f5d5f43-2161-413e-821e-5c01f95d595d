"""
Demo script for the Glass Cutting Optimization using Quantum Annealing

This script demonstrates the usage of the quantum annealing algorithm
for solving the glass cutting problem with guillotine cut constraints.
"""

import numpy as np
import matplotlib.pyplot as plt
from glass_cutting_optimizer import GlassCuttingOptimizer, Item
import time


def demo_basic_usage():
    """Demonstrate basic usage of the optimizer"""
    print("="*60)
    print("DEMO 1: Basic Usage")
    print("="*60)
    
    # Create optimizer
    material_width = 100.0
    optimizer = GlassCuttingOptimizer(material_width=material_width, lambda1=1.5)
    
    # Define items to cut
    items = [
        Item(0, 40, 30),  # Item 0: 40x30
        Item(1, 35, 25),  # Item 1: 35x25
        Item(2, 30, 20),  # Item 2: 30x20
        Item(3, 25, 35),  # Item 3: 25x35
    ]
    
    print(f"Material width: {material_width}")
    print(f"Items to cut:")
    for item in items:
        print(f"  {item}")
    
    # Add items to optimizer
    optimizer.add_items(items)
    
    # Solve the problem
    print(f"\nSolving with quantum annealing...")
    start_time = time.time()
    solution = optimizer.solve(num_reads=1000)
    solve_time = time.time() - start_time
    
    print(f"Solve time: {solve_time:.3f} seconds")
    print(f"Solution: {solution}")
    
    if solution.is_feasible:
        print(f"\nDetailed assignment:")
        for item_id, row_id in solution.assignment.items():
            item = items[item_id]
            print(f"  Item {item_id} ({item.width}x{item.height}) -> Row {row_id}")
        
        # Visualize solution
        print(f"\nVisualizing solution...")
        optimizer.visualize_solution(solution, save_path="demo_basic_solution.png")
    else:
        print("No feasible solution found!")


def demo_research_paper_example():
    """Demonstrate example based on the research paper"""
    print("\n" + "="*60)
    print("DEMO 2: Research Paper Example")
    print("="*60)
    
    # Create larger problem similar to research paper
    material_width = 200.0
    optimizer = GlassCuttingOptimizer(material_width=material_width, lambda1=2.0)
    
    # Items based on research paper patterns (approximated)
    items = [
        # Pattern A-like items
        Item(0, 70, 50),
        Item(1, 35, 25),
        Item(2, 30, 30),
        
        # Pattern B-like items  
        Item(3, 80, 60),
        Item(4, 40, 30),
        Item(5, 30, 25),
        
        # Pattern C-like items
        Item(6, 75, 55),
        Item(7, 45, 35),
        
        # Pattern D-like items
        Item(8, 90, 70),
        Item(9, 35, 40),
    ]
    
    print(f"Material width: {material_width}")
    print(f"Number of items: {len(items)}")
    print(f"Total item area: {sum(item.width * item.height for item in items)}")
    
    optimizer.add_items(items)
    
    # Solve multiple times as in the research paper
    print(f"\nSolving with multiple runs (as in research paper)...")
    start_time = time.time()
    solutions = optimizer.solve_multiple_runs(num_runs=10, num_reads=1000)
    total_time = time.time() - start_time
    
    # Get statistics
    stats = optimizer.get_statistics(solutions)
    
    print(f"Total time: {total_time:.3f} seconds")
    print(f"Average time per run: {total_time/10:.3f} seconds")
    print(f"\nResults:")
    print(f"  Total runs: {stats['total_runs']}")
    print(f"  Feasible solutions: {stats['feasible_count']}")
    print(f"  Feasible rate: {stats['feasible_rate']:.2%}")
    
    if stats['best_solution']:
        print(f"  Best solution: {stats['best_solution']}")
        print(f"  Average height: {stats['avg_height']:.2f} ± {stats['std_height']:.2f}")
        print(f"  Average waste: {stats['avg_waste']:.2f} ± {stats['std_waste']:.2f}")
        
        # Visualize best solution
        print(f"\nVisualizing best solution...")
        optimizer.visualize_solution(stats['best_solution'], save_path="demo_research_solution.png")
        
        # Show assignment details
        print(f"\nBest solution assignment:")
        for item_id, row_id in stats['best_solution'].assignment.items():
            item = items[item_id]
            print(f"  Item {item_id} ({item.width}x{item.height}) -> Row {row_id}")
    else:
        print("  No feasible solutions found!")


def demo_parameter_study():
    """Demonstrate the effect of different parameters"""
    print("\n" + "="*60)
    print("DEMO 3: Parameter Study")
    print("="*60)
    
    # Fixed problem setup
    material_width = 120.0
    items = [
        Item(0, 45, 35),
        Item(1, 30, 25),
        Item(2, 40, 30),
        Item(3, 35, 28),
        Item(4, 25, 20),
    ]
    
    # Test different lambda values
    lambda_values = [0.5, 1.0, 2.0, 5.0]
    results = {}
    
    print(f"Testing different lambda values: {lambda_values}")
    print(f"Material width: {material_width}")
    print(f"Items: {[f'{item.width}x{item.height}' for item in items]}")
    
    for lambda_val in lambda_values:
        print(f"\nTesting lambda = {lambda_val}")
        
        optimizer = GlassCuttingOptimizer(material_width=material_width, lambda1=lambda_val)
        optimizer.add_items(items)
        
        # Run multiple times for statistics
        solutions = optimizer.solve_multiple_runs(num_runs=5, num_reads=500)
        stats = optimizer.get_statistics(solutions)
        
        results[lambda_val] = stats
        
        print(f"  Feasible rate: {stats['feasible_rate']:.2%}")
        if stats['best_solution']:
            print(f"  Best waste: {stats['best_solution'].waste_area:.2f}")
            print(f"  Best height: {stats['best_solution'].total_height:.2f}")
    
    # Summary
    print(f"\nParameter Study Summary:")
    print(f"{'Lambda':<8} {'Feasible Rate':<15} {'Best Waste':<12} {'Best Height':<12}")
    print("-" * 50)
    for lambda_val, stats in results.items():
        feasible_rate = f"{stats['feasible_rate']:.1%}"
        best_waste = f"{stats['best_solution'].waste_area:.1f}" if stats['best_solution'] else "N/A"
        best_height = f"{stats['best_solution'].total_height:.1f}" if stats['best_solution'] else "N/A"
        print(f"{lambda_val:<8} {feasible_rate:<15} {best_waste:<12} {best_height:<12}")


def demo_scaling_analysis():
    """Demonstrate scaling behavior with problem size"""
    print("\n" + "="*60)
    print("DEMO 4: Scaling Analysis")
    print("="*60)
    
    problem_sizes = [3, 5, 7, 10]
    material_width = 150.0
    
    print(f"Testing scaling with problem sizes: {problem_sizes}")
    print(f"Material width: {material_width}")
    
    scaling_results = {}
    
    for size in problem_sizes:
        print(f"\nTesting problem size: {size} items")
        
        # Generate items with varying sizes
        items = []
        np.random.seed(42)  # For reproducible results
        for i in range(size):
            width = np.random.uniform(20, 50)
            height = np.random.uniform(15, 40)
            items.append(Item(i, width, height))
        
        optimizer = GlassCuttingOptimizer(material_width=material_width)
        optimizer.add_items(items)
        
        # Measure solve time
        start_time = time.time()
        solution = optimizer.solve(num_reads=500)
        solve_time = time.time() - start_time
        
        scaling_results[size] = {
            'solve_time': solve_time,
            'feasible': solution.is_feasible,
            'waste': solution.waste_area if solution.is_feasible else None,
            'height': solution.total_height if solution.is_feasible else None
        }
        
        print(f"  Solve time: {solve_time:.3f}s")
        print(f"  Feasible: {solution.is_feasible}")
        if solution.is_feasible:
            print(f"  Waste area: {solution.waste_area:.2f}")
            print(f"  Total height: {solution.total_height:.2f}")
    
    # Plot scaling results
    sizes = list(scaling_results.keys())
    times = [scaling_results[size]['solve_time'] for size in sizes]
    
    plt.figure(figsize=(10, 6))
    plt.subplot(1, 2, 1)
    plt.plot(sizes, times, 'bo-')
    plt.xlabel('Problem Size (number of items)')
    plt.ylabel('Solve Time (seconds)')
    plt.title('Scaling: Solve Time vs Problem Size')
    plt.grid(True)
    
    # Plot feasibility rate
    feasible_count = [1 if scaling_results[size]['feasible'] else 0 for size in sizes]
    plt.subplot(1, 2, 2)
    plt.bar(sizes, feasible_count)
    plt.xlabel('Problem Size (number of items)')
    plt.ylabel('Feasible (1) or Not (0)')
    plt.title('Feasibility vs Problem Size')
    plt.ylim(0, 1.2)
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('demo_scaling_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\nScaling analysis saved to 'demo_scaling_analysis.png'")


def main():
    """Run all demos"""
    print("Glass Cutting Optimization - Quantum Annealing Demo")
    print("Based on: 'Application of Ising Machine to optimize glass-plate cutting patterns'")
    print("by Takeshi Kida et al. (AGC Research Report 70, 2020)")
    
    try:
        # Run all demos
        demo_basic_usage()
        demo_research_paper_example()
        demo_parameter_study()
        demo_scaling_analysis()
        
        print("\n" + "="*60)
        print("All demos completed successfully!")
        print("Generated files:")
        print("  - demo_basic_solution.png")
        print("  - demo_research_solution.png") 
        print("  - demo_scaling_analysis.png")
        print("="*60)
        
    except Exception as e:
        print(f"\nError during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
